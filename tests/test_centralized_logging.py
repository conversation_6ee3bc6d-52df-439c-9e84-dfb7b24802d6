"""
Tests for the centralized logging system.
"""

import pytest
import json
import time
from unittest.mock import Mo<PERSON>, patch, MagicMock
from io import String<PERSON>

from app.logging.logger_manager import (
    <PERSON>ggerManager, ContextualLogger, StructuredFormatter, LogContext, get_logger
)
from app.logging.specialized_loggers import (
    JobLogger, APILogger, PerformanceLogger, SecurityLogger, DatabaseLogger
)


class TestLogContext:
    """Test LogContext class."""
    
    def test_context_management(self):
        """Test context setting and getting."""
        context = LogContext()
        
        # Set context
        context.set_context(job_uuid="test-123", step="download")
        
        # Get context
        ctx = context.get_context()
        assert ctx["job_uuid"] == "test-123"
        assert ctx["step"] == "download"
        
        # Update context
        context.set_context(status="processing")
        ctx = context.get_context()
        assert ctx["job_uuid"] == "test-123"
        assert ctx["step"] == "download"
        assert ctx["status"] == "processing"
        
        # Remove specific keys
        context.remove_context("step")
        ctx = context.get_context()
        assert "step" not in ctx
        assert ctx["job_uuid"] == "test-123"
        assert ctx["status"] == "processing"
        
        # Clear all context
        context.clear_context()
        ctx = context.get_context()
        assert len(ctx) == 0


class TestStructuredFormatter:
    """Test StructuredFormatter class."""
    
    def test_json_format(self):
        """Test JSON formatting."""
        formatter = StructuredFormatter(json_format=True)
        
        # Create mock log record
        record = Mock()
        record.levelname = "INFO"
        record.name = "test.logger"
        record.getMessage.return_value = "Test message"
        record.module = "test_module"
        record.funcName = "test_function"
        record.lineno = 42
        record.thread = 12345
        record.process = 67890
        record.exc_info = None
        record.__dict__ = {
            'name': 'test.logger',
            'levelname': 'INFO',
            'custom_field': 'custom_value'
        }
        
        # Format record
        formatted = formatter.format(record)
        
        # Parse JSON
        log_data = json.loads(formatted)
        
        assert log_data["level"] == "INFO"
        assert log_data["logger"] == "test.logger"
        assert log_data["message"] == "Test message"
        assert log_data["module"] == "test_module"
        assert log_data["function"] == "test_function"
        assert log_data["line"] == 42
        assert "timestamp" in log_data
    
    def test_human_readable_format(self):
        """Test human-readable formatting."""
        formatter = StructuredFormatter(json_format=False)
        
        # Create mock log record with context
        record = Mock()
        record.levelname = "INFO"
        record.name = "test.logger"
        record.getMessage.return_value = "Test message"
        record.module = "test_module"
        record.funcName = "test_function"
        record.lineno = 42
        record.thread = 12345
        record.process = 67890
        record.exc_info = None
        record._context = {"job_uuid": "test-123", "step": "download"}
        record.__dict__ = {
            'name': 'test.logger',
            'levelname': 'INFO',
            '_context': {"job_uuid": "test-123", "step": "download"}
        }
        
        # Format record
        formatted = formatter.format(record)
        
        assert "[INFO]" in formatted
        assert "[test.logger]" in formatted
        assert "Test message" in formatted
        assert "Context: job_uuid=test-123" in formatted
        assert "step=download" in formatted


class TestContextualLogger:
    """Test ContextualLogger class."""
    
    def test_logging_with_context(self):
        """Test logging with context."""
        # Create mock base logger
        base_logger = Mock()
        context_manager = LogContext()
        
        # Create contextual logger
        logger = ContextualLogger(base_logger, context_manager)
        
        # Set context
        logger.set_context(job_uuid="test-123")
        
        # Log message
        logger.info("Test message")
        
        # Verify log call
        base_logger.log.assert_called_once()
        args, kwargs = base_logger.log.call_args
        
        assert args[0] == 20  # INFO level
        assert args[1] == "Test message"
        assert 'extra' in kwargs
        assert kwargs['extra']['_context']['job_uuid'] == "test-123"


class TestJobLogger:
    """Test JobLogger class."""
    
    @patch('app.logging.specialized_loggers.get_logger')
    def test_job_lifecycle_logging(self, mock_get_logger):
        """Test job lifecycle logging."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        job_logger = JobLogger("test-job-123")
        
        # Test job start
        job_logger.log_job_start(param1="value1")
        mock_logger.info.assert_called_with("Job processing started", extra={'param1': 'value1'})
        
        # Test step context
        with job_logger.step_context("download", url="http://example.com"):
            pass
        
        # Verify step start and complete were called
        assert mock_logger.info.call_count >= 2
    
    @patch('app.logging.specialized_loggers.get_logger')
    def test_step_context_with_exception(self, mock_get_logger):
        """Test step context with exception."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        job_logger = JobLogger("test-job-123")
        
        # Test step context with exception
        with pytest.raises(ValueError):
            with job_logger.step_context("download"):
                raise ValueError("Test error")
        
        # Verify error was logged
        mock_logger.error.assert_called()


class TestAPILogger:
    """Test APILogger class."""
    
    @patch('app.logging.specialized_loggers.get_logger')
    def test_request_response_logging(self, mock_get_logger):
        """Test API request/response logging."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        api_logger = APILogger()
        
        # Test request logging
        api_logger.log_request("GET", "/api/test", client_ip="127.0.0.1")
        mock_logger.info.assert_called_with("GET /api/test", extra={
            'http_method': 'GET',
            'request_path': '/api/test',
            'event_type': 'request',
            'client_ip': '127.0.0.1'
        })
        
        # Test response logging
        api_logger.log_response("GET", "/api/test", 200, 0.5)
        mock_logger.info.assert_called_with("GET /api/test -> 200", extra={
            'http_method': 'GET',
            'request_path': '/api/test',
            'status_code': 200,
            'event_type': 'response',
            'response_time_seconds': 0.5
        })
    
    @patch('app.logging.specialized_loggers.get_logger')
    def test_request_context(self, mock_get_logger):
        """Test request context manager."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        api_logger = APILogger()
        
        # Test successful request
        with api_logger.request_context("POST", "/api/test"):
            time.sleep(0.01)  # Simulate processing time
        
        # Verify request and response were logged
        assert mock_logger.info.call_count == 2


class TestPerformanceLogger:
    """Test PerformanceLogger class."""
    
    @patch('app.logging.specialized_loggers.get_logger')
    def test_performance_metrics(self, mock_get_logger):
        """Test performance metrics logging."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        perf_logger = PerformanceLogger()
        
        # Test timing
        perf_logger.log_timing("database_query", 0.5, query="SELECT * FROM jobs")
        mock_logger.info.assert_called_with("Operation 'database_query' took 0.500s", extra={
            'operation': 'database_query',
            'duration_seconds': 0.5,
            'metric_type': 'timing',
            'query': 'SELECT * FROM jobs'
        })
        
        # Test counter
        perf_logger.log_counter("requests_processed", 100)
        mock_logger.info.assert_called_with("Metric 'requests_processed': 100", extra={
            'metric_name': 'requests_processed',
            'metric_value': 100,
            'metric_type': 'counter'
        })
        
        # Test timer context
        with perf_logger.timer("test_operation"):
            time.sleep(0.01)
        
        # Verify timing was logged
        mock_logger.info.assert_called()
        args, kwargs = mock_logger.info.call_args
        assert "test_operation" in args[0]


class TestSecurityLogger:
    """Test SecurityLogger class."""
    
    @patch('app.logging.specialized_loggers.get_logger')
    def test_security_events(self, mock_get_logger):
        """Test security event logging."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        security_logger = SecurityLogger()
        
        # Test successful auth
        security_logger.log_auth_attempt(True, "user123", "***********")
        mock_logger.info.assert_called_with("Authentication successful", extra={
            'auth_success': True,
            'user_id': 'user123',
            'ip_address': '***********',
            'event_type': 'auth_attempt'
        })
        
        # Test failed auth
        security_logger.log_auth_attempt(False, ip_address="***********")
        mock_logger.warning.assert_called_with("Authentication failed", extra={
            'auth_success': False,
            'user_id': None,
            'ip_address': '***********',
            'event_type': 'auth_attempt'
        })
        
        # Test suspicious activity
        security_logger.log_suspicious_activity("Multiple failed login attempts", 
                                               {"attempts": 5, "timespan": "5 minutes"})
        mock_logger.warning.assert_called_with("Suspicious activity detected: Multiple failed login attempts", extra={
            'suspicious_activity': 'Multiple failed login attempts',
            'event_type': 'suspicious_activity',
            'activity_details': {'attempts': 5, 'timespan': '5 minutes'}
        })


class TestLoggerManager:
    """Test LoggerManager class."""
    
    def test_singleton_pattern(self):
        """Test that LoggerManager is a singleton."""
        manager1 = LoggerManager()
        manager2 = LoggerManager()
        
        assert manager1 is manager2
    
    def test_get_logger(self):
        """Test getting logger instances."""
        manager = LoggerManager()
        
        logger1 = manager.get_logger("test.module")
        logger2 = manager.get_logger("test.module")
        
        # Should return the same instance
        assert logger1 is logger2
        assert isinstance(logger1, ContextualLogger)
    
    def test_global_context(self):
        """Test global context management."""
        manager = LoggerManager()
        
        # Set global context
        manager.set_global_context(service="test-service", version="1.0.0")
        
        # Get logger and check context
        logger = manager.get_logger("test")
        context = logger._context_manager.get_context()
        
        assert context["service"] == "test-service"
        assert context["version"] == "1.0.0"
        
        # Clear global context
        manager.clear_global_context()
        context = logger._context_manager.get_context()
        
        assert len(context) == 0


if __name__ == "__main__":
    pytest.main([__file__])
