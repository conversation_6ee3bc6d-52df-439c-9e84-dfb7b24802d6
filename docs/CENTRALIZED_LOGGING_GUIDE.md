# Merkezi Logger Sistemi Kullanım Rehberi

B<PERSON>, CloudML-v3 projesinde merkezi logger sisteminin nasıl kullanılacağını açıklar.

## 📋 Genel Bakış

Yeni merkezi logger sistemi şu bileşenlerden oluşur:

- **LoggerManager**: Singleton pattern ile merkezi logger yönetimi
- **ContextualLogger**: Thread-local context desteği ile logger
- **StructuredFormatter**: JSON ve human-readable format desteği
- **Specialized Loggers**: Farklı bileşenler için özelleştirilmiş logger'lar

## 🏗️ Mi<PERSON><PERSON>

```
LoggerManager (Singleton)
├── ContextualLogger (Thread-safe context)
├── StructuredFormatter (JSON/Human-readable)
└── Specialized Loggers
    ├── JobLogger (Job processing)
    ├── APILogger (HTTP requests/responses)
    ├── PerformanceLogger (Metrics)
    ├── SecurityLogger (Auth/Security events)
    └── DatabaseLogger (DB operations)
```

## 🚀 Temel Kullanım

### 1. <PERSON><PERSON><PERSON><PERSON>

```python
from app.logging.logger_manager import get_logger

# Logger oluştur (otomatik olarak modül adını kullanır)
logger = get_logger()

# Veya manuel olarak isim ver
logger = get_logger("my.module")

# Basit loglama
logger.info("İşlem başladı")
logger.error("Hata oluştu", extra={'error_code': 500})
```

### 2. Context ile Loglama

```python
from app.logging.logger_manager import get_logger

logger = get_logger(__name__)

# Context set et
logger.set_context(job_uuid="12345", user_id="user123")

# Bu loglar otomatik olarak context bilgilerini içerecek
logger.info("İşlem başladı")  # job_uuid ve user_id otomatik eklenir
logger.warning("Uyarı mesajı")

# Context temizle
logger.clear_context()
```

### 3. Global Context

```python
from app.logging.logger_manager import set_global_context, clear_global_context

# Tüm logger'lar için global context
set_global_context(
    service="object-detection-api",
    version="1.0.0",
    environment="production"
)

# Artık tüm loglar bu bilgileri içerecek
logger.info("Servis başlatıldı")

# Global context temizle
clear_global_context()
```

## 🎯 Specialized Logger'lar

### 1. Job Logger

```python
from app.logging.specialized_loggers import get_job_logger

# Job için özel logger
job_logger = get_job_logger("job-uuid-123")

# Job lifecycle logging
job_logger.log_job_start(video_url="http://example.com/video.mp4")

# Step context ile otomatik timing
with job_logger.step_context("download", url="http://example.com/video.mp4"):
    # Download işlemi
    download_video()
    # Otomatik olarak step başlangıç, bitiş ve süre loglanır

job_logger.log_job_complete(duration=120.5)
```

### 2. API Logger

```python
from app.logging.specialized_loggers import get_api_logger

api_logger = get_api_logger()

# Request/Response logging
with api_logger.request_context("POST", "/api/detect", client_ip="***********"):
    # API işlemi
    result = process_request()
    # Otomatik olarak request ve response loglanır
```

### 3. Performance Logger

```python
from app.logging.specialized_loggers import get_performance_logger

perf_logger = get_performance_logger()

# Timing ölçümü
with perf_logger.timer("database_query", table="jobs"):
    result = db.query("SELECT * FROM jobs")

# Manuel metrik loglama
perf_logger.log_timing("video_processing", 45.2, job_uuid="123")
perf_logger.log_counter("requests_processed", 1000)
perf_logger.log_gauge("memory_usage_mb", 512.5)
```

### 4. Security Logger

```python
from app.logging.specialized_loggers import get_security_logger

security_logger = get_security_logger()

# Authentication events
security_logger.log_auth_attempt(True, "user123", "***********")
security_logger.log_auth_token_validation(False, {"reason": "expired"})
security_logger.log_suspicious_activity("Brute force attempt", 
                                       {"attempts": 10, "timespan": "1 minute"})
```

### 5. Database Logger

```python
from app.logging.specialized_loggers import get_database_logger

db_logger = get_database_logger()

# Database operations
db_logger.log_query("SELECT", "jobs", duration=0.05, rows_returned=100)
db_logger.log_connection_event("pool_created", pool_size=10)
db_logger.log_migration("add_job_index", True)
```

## ⚙️ Konfigürasyon

### config.yaml Ayarları

```yaml
logging:
  level: "INFO"
  json_format: false  # true = JSON, false = human-readable
  include_context: true
  file_path: "/var/log/cloudml/app.log"
  max_file_size_mb: 100
  backup_count: 10
  
  # Component-specific levels
  components:
    api: "INFO"
    job_processing: "DEBUG"
    database: "WARNING"
    performance: "INFO"
    security: "INFO"
    celery: "WARNING"
  
  # Performance thresholds
  performance:
    enabled: true
    slow_query_threshold_seconds: 1.0
    slow_request_threshold_seconds: 5.0
```

### Environment Variables

```bash
# Override config.yaml settings
LOGGING_LEVEL=DEBUG
LOGGING_JSON_FORMAT=true
LOGGING_FILE_PATH=/var/log/app.log
LOGGING_COMPONENTS_API=DEBUG
LOGGING_PERFORMANCE_ENABLED=true
```

## 📊 Log Formatları

### JSON Format (json_format: true)

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "level": "INFO",
  "logger": "app.celery_app",
  "message": "Job processing started",
  "module": "celery_app",
  "function": "process_video",
  "line": 125,
  "context": {
    "job_uuid": "12345678-1234-1234-1234-123456789012",
    "service": "object-detection-api",
    "version": "1.0.0"
  },
  "extra": {
    "task_id": "celery-task-123",
    "worker_id": "worker-01"
  }
}
```

### Human-Readable Format (json_format: false)

```
[2024-01-15T10:30:00.000Z] [INFO] [app.celery_app] Job processing started | Context: job_uuid=12345678-1234-1234-1234-123456789012, service=object-detection-api, version=1.0.0 | Extra: task_id=celery-task-123, worker_id=worker-01
```

## 🔄 Migration Rehberi

### Eski Koddan Yeni Sisteme Geçiş

```python
# ESKİ YÖNTEMİ
import logging
from app.config.logging_config import setup_logging

setup_logging()
logger = logging.getLogger(__name__)

logger.info(f"Processing job {job_uuid}")

# YENİ YÖNTEMİ
from app.logging.logger_manager import get_logger
from app.logging.specialized_loggers import get_job_logger

logger = get_logger(__name__)
job_logger = get_job_logger(job_uuid)

# Context ile loglama
logger.set_context(job_uuid=job_uuid)
logger.info("Processing job")

# Veya specialized logger
job_logger.log_job_start()
```

### Celery Task Migration

```python
# ESKİ YÖNTEMİ
@celery_app.task
def process_video(job_uuid, params):
    logger.info(f"Starting job {job_uuid}")
    # işlem
    logger.info(f"Job {job_uuid} completed")

# YENİ YÖNTEMİ
@celery_app.task
def process_video(job_uuid, params):
    job_logger = get_job_logger(job_uuid)
    
    job_logger.log_job_start()
    
    with job_logger.step_context("download"):
        download_video()
    
    with job_logger.step_context("process"):
        process_video()
    
    job_logger.log_job_complete()
```

## 🧪 Testing

```python
from unittest.mock import patch
from app.logging.specialized_loggers import get_job_logger

@patch('app.logging.specialized_loggers.get_logger')
def test_job_logging(mock_get_logger):
    mock_logger = Mock()
    mock_get_logger.return_value = mock_logger
    
    job_logger = get_job_logger("test-job")
    job_logger.log_job_start(param="value")
    
    mock_logger.info.assert_called_with(
        "Job processing started", 
        extra={'param': 'value'}
    )
```

## 📈 Monitoring ve Alerting

### Log Aggregation

JSON formatı ile ELK Stack, Fluentd, veya diğer log aggregation sistemleri ile kolay entegrasyon:

```bash
# Elasticsearch query örneği
GET /logs/_search
{
  "query": {
    "bool": {
      "must": [
        {"term": {"level": "ERROR"}},
        {"term": {"context.service": "object-detection-api"}}
      ]
    }
  }
}
```

### Metrics Extraction

Performance logger'dan metrikler:

```bash
# Prometheus metrics örneği
video_processing_duration_seconds{job_uuid="123"} 45.2
download_speed_mbps{job_uuid="123"} 12.5
memory_usage_mb{component="detection"} 512.0
```

## 🚨 Best Practices

1. **Appropriate Log Levels**: DEBUG < INFO < WARNING < ERROR < CRITICAL
2. **Context Usage**: Her işlem için relevant context set edin
3. **Structured Data**: extra parametresi ile structured data ekleyin
4. **Performance**: Yoğun loop'larda DEBUG level kullanmayın
5. **Security**: Sensitive data'yı loglara yazmayın
6. **Error Handling**: Exception'ları context ile birlikte logla

## 🔧 Troubleshooting

### Common Issues

1. **Context Kaybolması**: Thread değişimlerinde context temizlenebilir
2. **Performance Impact**: JSON format daha yavaş, production'da dikkatli kullanın
3. **Log Rotation**: Büyük dosyalar için rotation ayarlarını optimize edin
4. **Memory Usage**: Context'te büyük objeler saklamayın

### Debug Mode

```python
# Debug mode için
from app.logging.logger_manager import get_logger

logger = get_logger(__name__)
logger.set_context(debug_mode=True, trace_id="debug-123")
```

Bu rehber ile merkezi logger sistemini etkili bir şekilde kullanabilirsiniz!
