# CloudML-v3 Documentation

Welcome to the CloudML-v3 documentation. This documentation provides a comprehensive overview of the system architecture, processing pipeline, and API.

## Table of Contents

1. [Architecture Overview](architecture.md) - High-level system architecture and components
2. [Processing Pipeline](processing-pipeline.md) - Detailed explanation of the video processing steps
3. [API and Data Models](api-and-models.md) - API endpoints, data models, and configuration

## System Overview

CloudML-v3 is a distributed video processing system that performs object detection on video files using machine learning models. The system is designed to detect various objects including people, animals, vehicles, packages, faces, license plates, and barcodes in videos.

The system follows a microservices architecture with the following key components:
- REST API for receiving processing requests
- Message queue (RabbitMQ) for task distribution
- Worker processes for executing detection tasks
- PostgreSQL database for job tracking
- Multiple ML models for different detection tasks

## Getting Started

For detailed information about the system, please refer to the individual documentation files linked above.

## Contributing

If you're interested in contributing to this project, please refer to the main repository README for guidelines.