# CloudML-v3 Architecture Documentation

## Overview

CloudML-v3 is a distributed video processing system that performs object detection on video files using machine learning models. The system is designed to detect various objects including people, animals, vehicles, packages, faces, license plates, and barcodes in videos.

The system follows a microservices architecture with the following key components:
- REST API for receiving processing requests
- Message queue (RabbitMQ) for task distribution
- Worker processes for executing detection tasks
- PostgreSQL database for job tracking
- Multiple ML models for different detection tasks

## System Components

### 1. Main API Service (`app/main.py`)

The main entry point is a FastAPI application that:
- Receives video processing requests via HTTP POST
- Validates incoming requests
- Creates jobs in the database
- Queues processing tasks via Celery
- Provides statistics and monitoring endpoints

#### Key Endpoints:
- `POST /object_detection` - Submit a video for object detection
- `GET /stats/queue` - Get count of queued jobs
- `GET /stats/queue/list` - Get list of queued jobs
- `GET /stats/completed` - Get count of completed jobs
- `GET /stats/failed` - Get count of failed jobs
- `GET /stats/processing` - Get count of processing jobs
- `GET /stats/workers` - Get worker information
- `GET /stats/job/{job_uuid}` - Get job history
- `GET /health` - Health check endpoint

### 2. Celery Worker (`app/celery_app.py`)

Processes video detection tasks asynchronously:
- Connects to RabbitMQ for task queuing
- Connects to PostgreSQL for job state tracking
- Executes the video processing pipeline
- Handles task retries and failures

### 3. Database Layer (`app/database.py`)

Uses SQLAlchemy with PostgreSQL:
- `Job` table for tracking job states
- `JobResult` table for storing detection results

### 4. Job Tracking (`app/job_tracking.py`)

Manages job lifecycle:
- Creating jobs
- Updating job states (queued, processing, completed, failed)
- Storing detection results
- Cleaning up expired jobs

### 5. Configuration (`app/config/`)

#### Settings (`app/config/settings.py`)
- Loads configuration from `config.yaml` and environment variables
- Supports hierarchical configuration with deep merging

#### Logging (`app/config/logging_config.py`)
- Configures logging with console and file handlers
- Supports log rotation

## Processing Pipeline

The video processing follows a step-based pipeline defined in `app/celery_app.py`:

### 1. Pre-processing (`app/step00_preprocess.py`)
Placeholder for initial preprocessing tasks.

### 2. Download File (`app/step10_downloadfile.py`)
Downloads the video file from the provided URL to local storage.

### 3. File Validation (`app/step20_filevalidate.py`)
Placeholder for file validation.

### 4. Object Detection (`app/step30_before_check.py`)
Main detection orchestrator that calls specialized detection modules:

#### 4.1 Standard Detection (`app/step32_standard_detection.py`)
Detects people, animals, and vehicles using YOLO models.

#### 4.2 Package Detection
Three approaches for package detection:
- Custom model (`app/step33_package_detection_custom.py`)
- Frame difference method (`app/step33_package_detection_diff_frame.py`)
- YOLO World model (`app/step33_package_detection_yoloworld.py`)

#### 4.3 Face Recognition (`app/step34_face_recognition.py`)
Performs face recognition using the face_recognition library.

#### 4.4 License Plate Detection (`app/step35_licence_plate.py`)
Detects and recognizes license plates with OCR processing (`app/step35_ocr_process.py`).

#### 4.5 Barcode Detection (`app/step36_barcode.py`)
Detects QR codes and barcodes using OpenCV.

### 5. Send Results (`app/step40_sendresult.py`)
Sends detection results to the callback URL provided in the request.

### 6. Clean Environment (`app/step50_cleanEnvironment.py`)
Cleans up temporary files created during processing.

## Data Models

Defined in `app/models.py`:
- Request/response models for the API
- Configuration models for detection parameters
- Job tracking models
- Detection result models

## Request Validation

Handled by `app/request_validation.py`:
- Validates authentication tokens (JWT)
- Checks job uniqueness
- Enriches request parameters with configuration values
- Validates detection configuration

## Helper Functions

Utility functions in `app/helper.py`:
- Object label UUID mapping
- Hot zone preparation for region-based detection
- Detection result preparation
- Class mapping helpers
- OpenTelemetry tracing
- Model class indexing

## ML Models

Located in `app/ezlomodels/`:
- Standard YOLO models for person/animal/vehicle detection
- Custom package detection models
- License plate detection model
- YOLO World model for package detection

## Configuration

The system can be configured via:
1. `config.yaml` file
2. Environment variables (takes precedence)

Key configuration areas:
- RabbitMQ connection
- PostgreSQL connection
- Logging settings
- Model paths
- Detection thresholds
- Authentication settings
- OpenTelemetry settings

## Deployment

The system is designed for containerized deployment with Docker:
- `DockerfileApi` - API service
- `DockerfileWorker` - Worker processes
- `DockerfileOcr` - OCR service
- `DockerfileOpenALPR` - OpenALPR service
- `docker-compose.yml` - Multi-container setup

## Data Flow

1. Client sends a POST request to `/object_detection`
2. API validates the request and creates a job in PostgreSQL
3. API queues a task via Celery to RabbitMQ
4. Worker picks up the task from RabbitMQ
5. Worker executes the processing pipeline:
   - Download video
   - Perform object detection using appropriate models
   - Store results in PostgreSQL
6. Worker sends results to callback URL
7. Worker cleans up temporary files

## Error Handling

- Each step in the pipeline has proper error handling
- Failed jobs are marked in the database
- Error messages are sent to the callback URL
- Worker handles retries for transient failures
- Proper logging at each step for debugging

## Monitoring and Observability

- OpenTelemetry tracing for request tracking
- Detailed logging at each processing step
- Job state tracking in PostgreSQL
- Health check endpoint
- Statistics endpoints for queue monitoring