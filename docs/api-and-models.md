# Data Models and API Documentation

## API Endpoints

### POST /object_detection

Submit a video for object detection.

**Request Headers:**
- Authorization: Bearer <JWT_TOKEN> (if authentication is enabled)

**Request Body:**
```json
{
  "ot": {
    "trace_id": "string (optional)",
    "span_id": "string (optional)"
  },
  "resources": {
    "method": "string",
    "url": "string"
  },
  "file": {
    "uuid": "string",
    "timestamp": "integer (optional)",
    "size": "integer (optional)",
    "filename": "string (optional)",
    "file_format": "string (optional)",
    "meta": "string (optional)"
  },
  "db": {
    "elements": {
      "known_people": [
        {
          "uuid": "string",
          "name": "string",
          "meta": "object",
          "files": [
            {
              "uuid": "string",
              "meta": "object"
            }
          ]
        }
      ]
    },
    "configuration": {
      "standard_skip_frame": "number (optional)",
      "package_skip_frame": "number (optional)",
      "global_threshold": "number (optional)",
      "person_detection": {
        "enabled": "boolean (optional)"
      },
      "package_detection": {
        "enabled": "boolean (optional)"
      },
      "animal_detection": {
        "enabled": "boolean (optional)",
        "items": ["string (optional)"]
      },
      "face_recognition": {
        "enabled": "boolean (optional)",
        "items": ["string (optional)"]
      },
      "barcode_detection": {
        "enabled": "boolean (optional)",
        "items": ["string (optional)"]
      },
      "vehicle_detection": {
        "enabled": "boolean (optional)",
        "items": ["string (optional)"]
      },
      "plate_detection": {
        "enabled": "boolean (optional)",
        "items": ["string (optional)"]
      },
      "hotzones": {
        "enabled": "boolean",
        "zones": [
          {
            "id": "integer",
            "maxwidth": "integer",
            "maxheight": "integer",
            "points": [
              {
                "x": "integer",
                "y": "integer"
              }
            ]
          }
        ]
      }
    },
    "files": "object (optional)"
  },
  "response": {
    "callback": "string"
  }
}
```

**Response:**
```json
{
  "status": "string",
  "status_code": "integer",
  "task_id": "string",
  "uuid": "string",
  "message": "string"
}
```

### GET /stats/queue

Get the count of queued jobs.

**Response:**
```json
{
  "count": "integer"
}
```

### GET /stats/queue/list

Get the list of queued jobs.

**Response:**
```json
{
  "jobs": [
    {
      "uuid": "string",
      "state": "string",
      "step": "string",
      "task_id": "string",
      "created_at": "string",
      "updated_at": "string",
      "expires_at": "string",
      "ot_traceid": "string",
      "ot_spanid": "string",
      "error_message": "string"
    }
  ]
}
```

### GET /stats/completed

Get the count of completed jobs.

**Response:**
```json
{
  "count": "integer"
}
```

### GET /stats/failed

Get the count of failed jobs.

**Response:**
```json
{
  "count": "integer"
}
```

### GET /stats/processing

Get the count of processing jobs.

**Response:**
```json
{
  "count": "integer"
}
```

### GET /stats/workers

Get worker information.

**Response:**
```json
{
  "workers": []
}
```

### GET /stats/job/{job_uuid}

Get job history.

**Response:**
```json
{
  "job": {
    "uuid": "string",
    "state": "string",
    "step": "string",
    "task_id": "string",
    "created_at": "string",
    "updated_at": "string",
    "expires_at": "string",
    "ot_traceid": "string",
    "ot_spanid": "string",
    "error_message": "string"
  }
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "status": "healthy"
}
```

## Data Models

### TaskParameters

Extends DetectionRequest with additional processing parameters:

- `object_detection_device`: Device to use for detection (cpu/gpu)
- `model_standard_path`: Path to standard YOLO model
- `model_package_path`: Path to package detection model
- `model_custom_package_path`: Path to custom package model
- `model_license_plate_path`: Path to license plate detection model
- `license_plate_recognition_country`: Country code for license plate recognition
- `person_detection_classes`: List of classes for person detection
- `animal_detection_classes`: List of classes for animal detection
- `vehicle_detection_classes`: List of classes for vehicle detection
- `package_detection_classes`: List of classes for package detection
- `facerec_const_tolerance`: Tolerance for face recognition
- `hostname`: Hostname of the processing worker
- `controller_uuid`: UUID of the controller
- `device_id`: ID of the device
- `ocr_server_url`: URL of the OCR server
- `openalpr_server_url`: URL of the OpenALPR server

### DetectionData

Represents a single detection result:

- `label`: Label of the detected object
- `label_element`: UUID of the label element
- `timestamp`: Timestamp of detection
- `milisec`: Millisecond of detection
- `class`: Class of the detected object
- `conf`: Confidence of the detection
- `bbox`: Bounding box coordinates (xmin, ymin, xmax, ymax)
- `known_people_uuid`: UUID of known person (for face recognition)

### Job

Database model for tracking jobs:

- `uuid`: Unique identifier for the job
- `state`: Current state of the job (queued, processing, completed, failed)
- `step`: Current step in the processing pipeline
- `task_id`: Celery task ID
- `created_at`: Timestamp when the job was created
- `updated_at`: Timestamp when the job was last updated
- `expires_at`: Timestamp when the job expires
- `ot_traceid`: OpenTelemetry trace ID
- `ot_spanid`: OpenTelemetry span ID
- `error_message`: Error message if the job failed

### JobResult

Database model for storing detection results:

- `job_uuid`: UUID of the associated job
- `result`: JSON object containing the detection result
- `created_at`: Timestamp when the result was created

## Configuration

### System Configuration

The system can be configured via `config.yaml` and environment variables:

**Key Configuration Options:**

1. **RabbitMQ Configuration:**
   - `rabbitmq.username`: RabbitMQ username
   - `rabbitmq.password`: RabbitMQ password
   - `rabbitmq.host`: RabbitMQ host
   - `rabbitmq.port`: RabbitMQ port
   - `rabbitmq.vhost`: RabbitMQ virtual host

2. **PostgreSQL Configuration:**
   - `postgresql.username`: PostgreSQL username
   - `postgresql.password`: PostgreSQL password
   - `postgresql.host`: PostgreSQL host
   - `postgresql.port`: PostgreSQL port
   - `postgresql.database`: PostgreSQL database name
   - `postgresql.echo`: Whether to echo SQL statements

3. **API Configuration:**
   - `api.host`: API host
   - `api.port`: API port
   - `api.reload`: Whether to reload on code changes

4. **Authentication Configuration:**
   - `auth.enabled`: Whether authentication is enabled
   - `auth.secret_key`: JWT secret key
   - `auth.algorithm`: JWT algorithm
   - `auth.allowed_issuers`: List of allowed JWT issuers

5. **Logging Configuration:**
   - `logging.level`: Log level
   - `logging.format`: Log format
   - `logging.file_path`: Log file path
   - `logging.max_file_size_mb`: Maximum log file size in MB
   - `logging.backup_count`: Number of backup log files

6. **Worker Configuration:**
   - `worker.prefetch_multiplier`: Celery prefetch multiplier
   - `worker.concurrency`: Celery worker concurrency
   - `worker.max_tasks_per_child`: Maximum tasks per worker child

7. **Object Detection Configuration:**
   - `object_detection.device`: Device to use for detection (cpu/gpu)
   - `object_detection.person_detection_classes`: Classes for person detection
   - `object_detection.animal_detection_classes`: Classes for animal detection
   - `object_detection.vehicle_detection_classes`: Classes for vehicle detection
   - `object_detection.package_detection_classes`: Classes for package detection
   - `object_detection.global_confidence_threshold`: Global confidence threshold
   - `standard.skip_frame_ratio`: Skip frame ratio for standard detection
   - `package.skip_frame_ratio`: Skip frame ratio for package detection

8. **Model Configuration:**
   - `standard.model`: Path to standard model
   - `package.model`: Path to package model
   - `custom_package.model`: Path to custom package model
   - `license_plate_recognition.model`: Path to license plate model
   - `license_plate_recognition.country`: Country for license plate recognition

9. **Face Recognition Configuration:**
   - `face_recognition.const_tolerance`: Constant tolerance for face recognition

10. **Service URLs:**
    - `ocr_server.url`: URL of the OCR server
    - `openalpr_server.url`: URL of the OpenALPR server

11. **Job Tracking Configuration:**
    - `job_tracking.retention_days`: Number of days to retain job records

12. **File Processing Configuration:**
    - `file_processing.video_temp_directory`: Temporary directory for video files

13. **OpenTelemetry Configuration:**
    - `open_telemetry.collector_endpoint`: OpenTelemetry collector endpoint

### Environment Variables

All configuration options can be overridden with environment variables by converting the dot notation to uppercase with underscores. For example:
- `RABBITMQ_USERNAME` for `rabbitmq.username`
- `POSTGRESQL_PASSWORD` for `postgresql.password`
- `API_PORT` for `api.port`

## Authentication

When authentication is enabled (`auth.enabled: true`), requests to the API must include a JWT token in the Authorization header:

```
Authorization: Bearer <JWT_TOKEN>
```

The token must:
1. Be properly signed with the configured secret key
2. Not be expired
3. Have a valid issuer from the allowed issuers list
4. Have appropriate timestamps (iat, exp, nbf)