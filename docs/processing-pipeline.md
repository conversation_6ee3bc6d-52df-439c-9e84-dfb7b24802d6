# Video Processing Pipeline

## Overview

The video processing pipeline in CloudML-v3 consists of several sequential steps that transform a video file into detected objects. Each step is implemented as a separate module to maintain modularity and facilitate testing.

## Step-by-Step Processing

### Step 0: Pre-processing (`app/step00_preprocess.py`)

This is a placeholder step for any pre-processing tasks that might be needed before downloading the video file. Currently, it's an empty implementation.

### Step 10: Download File (`app/step10_downloadfile.py`)

Downloads the video file from the URL provided in the request:

1. Updates job state to "downloadfile/started"
2. Validates the resource URL
3. Downloads the file using the `requests` library with streaming
4. Saves the file to a temporary location
5. Validates the downloaded file (checks existence and size)
6. Updates job state to "downloadfile/finished" or "downloadfile/failed"

**Error Handling:**
- Empty URL
- Network errors during download
- HTTP errors from the source server
- File system errors
- Empty or corrupted downloads

### Step 20: File Validation (`app/step20_filevalidate.py`)

Placeholder for file validation. Currently empty.

### Step 30: Before Check (`app/step30_before_check.py`)

Prepares for object detection:

1. Verifies the downloaded file exists
2. Calls the main object detection module (`step31_objectDetection.py`)
3. Handles errors in the detection process

### Step 31: Object Detection Orchestrator (`app/step31_objectDetection.py`)

The main orchestrator for all object detection tasks:

1. Loads task parameters
2. Sets up device (CPU/GPU) for processing
3. Prepares hot zones for region-based detection
4. Executes specialized detection modules based on request configuration:
   - Standard detection (people, animals, vehicles)
   - Package detection (custom, diff frame, YOLO World)
   - Face recognition
   - License plate detection
   - Barcode detection
5. Sends OpenTelemetry traces for each detection type

### Step 32: Standard Detection (`app/step32_standard_detection.py`)

Detects people, animals, and vehicles using YOLO models:

1. Loads the standard YOLO model
2. Processes video frames with configurable skip frames
3. Applies confidence threshold filtering
4. Processes detections within hot zones if configured
5. Records detected objects

**Key Features:**
- Batch processing for efficiency
- Configurable confidence thresholds
- Hot zone filtering
- Device selection (CPU/GPU)

### Step 33: Package Detection

Three different approaches for package detection:

#### Custom Package Detection (`app/step33_package_detection_custom.py`)

Uses a custom-trained model for package detection:

1. Loads the custom package model
2. Processes video frames with configurable skip frames
3. Applies confidence threshold filtering
4. Processes detections within hot zones if configured
5. Returns detected packages

#### Frame Difference Detection (`app/step33_package_detection_diff_frame.py`)

Detects packages by comparing the first and last frames of a video:

1. Extracts first and last frames
2. Computes frame difference to identify changes
3. Crops the region with the most changes
4. Runs package detection on the cropped region
5. Returns detected packages

#### YOLO World Detection (`app/step33_package_detection_yoloworld.py`)

Uses YOLO World model for zero-shot package detection:

1. Loads the YOLO World model
2. Sets detection classes based on configuration
3. Processes video frames with configurable skip frames
4. Applies confidence threshold filtering
5. Processes detections within hot zones if configured
6. Returns detected packages

### Step 34: Face Recognition (`app/step34_face_recognition.py`)

Performs face recognition using the face_recognition library:

1. Loads known faces from provided URLs
2. Processes video frames with configurable skip frames
3. Detects faces in each frame
4. Compares detected faces with known faces
5. Records recognized faces

**Key Features:**
- Configurable tolerance for face matching
- Support for filtering recognized faces
- URL-based known face images

### Step 35: License Plate Detection (`app/step35_licence_plate.py`)

Detects and recognizes license plates:

1. Uses YOLO model to detect license plates in video frames
2. Crops detected license plates
3. Processes cropped images for OCR
4. Performs OCR using:
   - Custom OCR service
   - OpenALPR service
5. Records recognized license plates

**Supporting Module:** `app/step35_ocr_process.py`
- Image preprocessing for better OCR results
- Deskewing, grayscaling, sharpening, contrast adjustment
- Noise reduction and thresholding
- Integration with OCR and OpenALPR services

### Step 36: Barcode Detection (`app/step36_barcode.py`)

Detects QR codes and barcodes using OpenCV:

1. Processes video frames
2. Detects QR codes and barcodes
3. Decodes detected codes
4. Records decoded values

### Step 40: Send Results (`app/step40_sendresult.py`)

Sends detection results to the callback URL:

1. Updates job state to "sendresult/started"
2. Retrieves detection results from the database
3. Formats results according to API specification
4. Sends results to the callback URL via HTTP POST
5. Updates job state to "sendresult/finished" or "sendresult/failed"

**Error Handling:**
- Network timeouts
- Connection errors
- HTTP errors from callback server
- JSON serialization errors

### Step 50: Clean Environment (`app/step50_cleanEnvironment.py`)

Cleans up temporary files:

1. Updates job state to "cleanenv/started"
2. Removes downloaded video file
3. Updates job state to "cleanenv/finished" or "cleanenv/failed"

## Configuration Options

Each step can be configured via the request parameters and system configuration:

- Skip frame ratios for performance tuning
- Confidence thresholds for filtering detections
- Device selection (CPU/GPU)
- Hot zone definitions for region-based detection
- Model paths
- OCR service URLs

## Error Propagation

Errors in any step will:
1. Update the job state to "failed"
2. Record the error message in the database
3. Trigger the send results step with error information
4. Stop further processing