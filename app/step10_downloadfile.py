"""
File download step with centralized logging and error handling.

This module handles video file downloads with proper error handling and logging.
"""

import os
import sys
import time
import urllib

import requests

from app.config.logging_config import setup_logging
from app.job_tracking import job_tracker
from app.logging.logger_manager import get_logger
from app.logging.specialized_loggers import get_performance_logger

setup_logging()
logger = get_logger(__name__)
performance_logger = get_performance_logger()


def step10_downloadFile_run(job_uuid: str, resource_url: str, video_path: str):
    """
    Download video file with centralized logging and performance monitoring.

    Args:
        job_uuid: Job UUID for tracking
        resource_url: URL to download from
        video_path: Local path to save the file

    Raises:
        Exception: If download fails
    """
    # Set context for this operation
    logger.set_context(job_uuid=job_uuid, operation="download")
    start_time = time.time()

    try:
        job_tracker.update_job_state(job_uuid, "downloadfile", "started")

        if not resource_url:
            raise Exception("Resource URL is empty")

        logger.info("Starting video download", extra={
            'resource_url': resource_url,
            'video_path': video_path
        })

        # Download with performance monitoring
        download_start = time.time()
        bytes_downloaded = 0

        try:
            with requests.get(resource_url, allow_redirects=True, stream=True, timeout=30) as response:
                response.raise_for_status()

                with open(video_path, "wb") as out_file:
                    for chunk in response.iter_content(chunk_size=1024 * 1024 * 2):
                        if chunk:
                            out_file.write(chunk)
                            bytes_downloaded += len(chunk)

        except requests.exceptions.RequestException as e:
            raise Exception(f"Download request failed: {str(e)}")
        except OSError as e:
            raise Exception(f"File write error: {str(e)}")

        download_duration = time.time() - download_start

        # Validate downloaded file
        if not os.path.exists(video_path):
            raise Exception("Video file was not downloaded successfully")

        file_size = os.path.getsize(video_path)
        if file_size == 0:
            raise Exception("Downloaded video file is empty")

        # Log performance metrics
        download_speed_mbps = (file_size / (1024 * 1024)) / download_duration if download_duration > 0 else 0
        performance_logger.log_timing("video_download", download_duration,
                                    job_uuid=job_uuid, file_size_mb=file_size/(1024*1024))
        performance_logger.log_gauge("download_speed_mbps", download_speed_mbps, job_uuid=job_uuid)

        logger.info("Video download completed successfully", extra={
            'file_size_bytes': file_size,
            'download_duration_seconds': download_duration,
            'download_speed_mbps': download_speed_mbps
        })

    except Exception as err:
        job_tracker.update_job_state(job_uuid, "downloadfile", "failed")

        # Log error with context
        duration = time.time() - start_time
        logger.error("Video download failed", extra={
            'error_type': type(err).__name__,
            'error_message': str(err),
            'duration_seconds': duration,
            'resource_url': resource_url,
            'video_path': video_path
        })

        raise Exception(f"Download failed: {str(err)}")
    finally:
        job_tracker.update_job_state(job_uuid, "downloadfile", "finished")
        logger.clear_context()
