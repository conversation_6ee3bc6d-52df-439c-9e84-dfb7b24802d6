import logging.handlers
import os
import yaml
from typing import Any, Dict
from dotenv import load_dotenv

load_dotenv(".env.local")

DEFAULT_CONFIG = {}

class Config:
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self._load_config(config_path)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        config = DEFAULT_CONFIG.copy()

        # Load config from file if it exists
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                file_config = yaml.safe_load(f)
                if file_config:
                    config = self._deep_merge(config, file_config)
        return config

    def _deep_merge(self, base: Dict, override: Dict) -> Dict:
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                base[key] = self._deep_merge(base[key], value)
            else:
                base[key] = value
        return base

    def get(self, key_path: str, default=None):
        # Convert dot notation to env var style
        env_var_name = key_path.upper().replace('.', '_')
        env_value = os.getenv(env_var_name)

        if env_value is not None:
            if isinstance(default, bool):
                logging.warning(f"get BOOL {key_path} {default} {env_value}")
                return env_value.lower() == "true"
            return env_value

        keys = key_path.split('.')
        value = self.config

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

config = Config(config_path="config.yaml")