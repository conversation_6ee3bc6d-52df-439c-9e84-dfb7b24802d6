"""
Specialized loggers for different components of CloudML-v3.

This module provides pre-configured loggers for specific use cases
like job processing, API requests, performance monitoring, etc.
"""

import time
from typing import Dict, Any, Optional, Union
from datetime import datetime
from contextlib import contextmanager

from app.logging.logger_manager import get_logger, ContextualLogger


class JobLogger:
    """Specialized logger for job processing with automatic context management."""
    
    def __init__(self, job_uuid: str, logger_name: str = None):
        self.job_uuid = job_uuid
        self.logger = get_logger(logger_name or f"job.{job_uuid[:8]}")
        self.logger.set_context(job_uuid=job_uuid)
        self._step_start_time = None
    
    def log_job_start(self, **kwargs):
        """Log job start with context."""
        self.logger.info("Job processing started", extra=kwargs)
    
    def log_job_complete(self, duration: float = None, **kwargs):
        """Log job completion."""
        extra = kwargs.copy()
        if duration is not None:
            extra['duration_seconds'] = duration
        self.logger.info("Job processing completed", extra=extra)
    
    def log_job_failed(self, error: Exception, **kwargs):
        """Log job failure."""
        extra = kwargs.copy()
        extra.update({
            'error_type': type(error).__name__,
            'error_message': str(error)
        })
        self.logger.error("Job processing failed", extra=extra)
    
    def log_step_start(self, step: str, **kwargs):
        """Log processing step start."""
        self._step_start_time = time.time()
        self.logger.set_context(current_step=step)
        self.logger.info(f"Step '{step}' started", extra=kwargs)
    
    def log_step_complete(self, step: str, **kwargs):
        """Log processing step completion."""
        extra = kwargs.copy()
        if self._step_start_time:
            duration = time.time() - self._step_start_time
            extra['step_duration_seconds'] = duration
            self._step_start_time = None
        
        self.logger.info(f"Step '{step}' completed", extra=extra)
        self.logger.remove_context('current_step')
    
    def log_step_failed(self, step: str, error: Exception, **kwargs):
        """Log processing step failure."""
        extra = kwargs.copy()
        extra.update({
            'error_type': type(error).__name__,
            'error_message': str(error)
        })
        if self._step_start_time:
            duration = time.time() - self._step_start_time
            extra['step_duration_seconds'] = duration
            self._step_start_time = None
        
        self.logger.error(f"Step '{step}' failed", extra=extra)
        self.logger.remove_context('current_step')
    
    @contextmanager
    def step_context(self, step: str, **kwargs):
        """Context manager for step logging."""
        self.log_step_start(step, **kwargs)
        try:
            yield self
        except Exception as e:
            self.log_step_failed(step, e)
            raise
        else:
            self.log_step_complete(step)


class APILogger:
    """Specialized logger for API requests and responses."""
    
    def __init__(self, logger_name: str = "api"):
        self.logger = get_logger(logger_name)
    
    def log_request(self, method: str, path: str, **kwargs):
        """Log incoming API request."""
        extra = kwargs.copy()
        extra.update({
            'http_method': method,
            'request_path': path,
            'event_type': 'request'
        })
        self.logger.info(f"{method} {path}", extra=extra)
    
    def log_response(self, method: str, path: str, status_code: int, 
                    duration: float = None, **kwargs):
        """Log API response."""
        extra = kwargs.copy()
        extra.update({
            'http_method': method,
            'request_path': path,
            'status_code': status_code,
            'event_type': 'response'
        })
        if duration is not None:
            extra['response_time_seconds'] = duration
        
        level = 'info' if status_code < 400 else 'warning' if status_code < 500 else 'error'
        getattr(self.logger, level)(
            f"{method} {path} -> {status_code}", 
            extra=extra
        )
    
    def log_validation_error(self, method: str, path: str, errors: list, **kwargs):
        """Log validation errors."""
        extra = kwargs.copy()
        extra.update({
            'http_method': method,
            'request_path': path,
            'validation_errors': errors,
            'event_type': 'validation_error'
        })
        self.logger.warning(f"Validation failed for {method} {path}", extra=extra)
    
    @contextmanager
    def request_context(self, method: str, path: str, **kwargs):
        """Context manager for request logging."""
        start_time = time.time()
        self.log_request(method, path, **kwargs)
        
        try:
            yield self
        except Exception as e:
            duration = time.time() - start_time
            self.log_response(method, path, 500, duration, error=str(e))
            raise


class PerformanceLogger:
    """Specialized logger for performance monitoring."""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = get_logger(logger_name)
    
    def log_timing(self, operation: str, duration: float, **kwargs):
        """Log operation timing."""
        extra = kwargs.copy()
        extra.update({
            'operation': operation,
            'duration_seconds': duration,
            'metric_type': 'timing'
        })
        self.logger.info(f"Operation '{operation}' took {duration:.3f}s", extra=extra)
    
    def log_counter(self, metric: str, value: Union[int, float], **kwargs):
        """Log counter metric."""
        extra = kwargs.copy()
        extra.update({
            'metric_name': metric,
            'metric_value': value,
            'metric_type': 'counter'
        })
        self.logger.info(f"Metric '{metric}': {value}", extra=extra)
    
    def log_gauge(self, metric: str, value: Union[int, float], **kwargs):
        """Log gauge metric."""
        extra = kwargs.copy()
        extra.update({
            'metric_name': metric,
            'metric_value': value,
            'metric_type': 'gauge'
        })
        self.logger.info(f"Gauge '{metric}': {value}", extra=extra)
    
    def log_memory_usage(self, component: str, memory_mb: float, **kwargs):
        """Log memory usage."""
        extra = kwargs.copy()
        extra.update({
            'component': component,
            'memory_mb': memory_mb,
            'metric_type': 'memory'
        })
        self.logger.info(f"Memory usage for '{component}': {memory_mb:.2f}MB", extra=extra)
    
    @contextmanager
    def timer(self, operation: str, **kwargs):
        """Context manager for timing operations."""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.log_timing(operation, duration, **kwargs)


class SecurityLogger:
    """Specialized logger for security events."""
    
    def __init__(self, logger_name: str = "security"):
        self.logger = get_logger(logger_name)
    
    def log_auth_attempt(self, success: bool, user_id: str = None, 
                        ip_address: str = None, **kwargs):
        """Log authentication attempt."""
        extra = kwargs.copy()
        extra.update({
            'auth_success': success,
            'user_id': user_id,
            'ip_address': ip_address,
            'event_type': 'auth_attempt'
        })
        
        if success:
            self.logger.info("Authentication successful", extra=extra)
        else:
            self.logger.warning("Authentication failed", extra=extra)
    
    def log_auth_token_validation(self, valid: bool, token_info: dict = None, **kwargs):
        """Log token validation."""
        extra = kwargs.copy()
        extra.update({
            'token_valid': valid,
            'event_type': 'token_validation'
        })
        if token_info:
            extra['token_info'] = token_info
        
        if valid:
            self.logger.debug("Token validation successful", extra=extra)
        else:
            self.logger.warning("Token validation failed", extra=extra)
    
    def log_suspicious_activity(self, activity: str, details: dict = None, **kwargs):
        """Log suspicious activity."""
        extra = kwargs.copy()
        extra.update({
            'suspicious_activity': activity,
            'event_type': 'suspicious_activity'
        })
        if details:
            extra['activity_details'] = details
        
        self.logger.warning(f"Suspicious activity detected: {activity}", extra=extra)


class DatabaseLogger:
    """Specialized logger for database operations."""
    
    def __init__(self, logger_name: str = "database"):
        self.logger = get_logger(logger_name)
    
    def log_query(self, query_type: str, table: str, duration: float = None, **kwargs):
        """Log database query."""
        extra = kwargs.copy()
        extra.update({
            'query_type': query_type,
            'table': table,
            'event_type': 'db_query'
        })
        if duration is not None:
            extra['query_duration_seconds'] = duration
        
        self.logger.debug(f"Database {query_type} on {table}", extra=extra)
    
    def log_connection_event(self, event: str, **kwargs):
        """Log database connection events."""
        extra = kwargs.copy()
        extra.update({
            'connection_event': event,
            'event_type': 'db_connection'
        })
        self.logger.info(f"Database connection: {event}", extra=extra)
    
    def log_migration(self, migration: str, success: bool, **kwargs):
        """Log database migration."""
        extra = kwargs.copy()
        extra.update({
            'migration': migration,
            'migration_success': success,
            'event_type': 'db_migration'
        })
        
        if success:
            self.logger.info(f"Migration '{migration}' completed", extra=extra)
        else:
            self.logger.error(f"Migration '{migration}' failed", extra=extra)


# Pre-configured logger instances
job_logger_factory = lambda job_uuid: JobLogger(job_uuid)
api_logger = APILogger()
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()
database_logger = DatabaseLogger()


# Utility functions
def get_job_logger(job_uuid: str) -> JobLogger:
    """Get a job logger for specific job UUID."""
    return JobLogger(job_uuid)


def get_api_logger() -> APILogger:
    """Get the API logger instance."""
    return api_logger


def get_performance_logger() -> PerformanceLogger:
    """Get the performance logger instance."""
    return performance_logger


def get_security_logger() -> SecurityLogger:
    """Get the security logger instance."""
    return security_logger


def get_database_logger() -> DatabaseLogger:
    """Get the database logger instance."""
    return database_logger
