"""
Centralized Logger Manager for CloudML-v3 project.

This module provides a centralized logging system with structured logging,
context management, and consistent formatting across all modules.
"""

import logging
import json
import sys
import threading
from datetime import datetime
from typing import Dict, Any, Optional, Union
from enum import Enum
from pathlib import Path
from logging.handlers import <PERSON>otating<PERSON><PERSON><PERSON>and<PERSON>, TimedRotatingFileHandler

from app.config.settings import config


class LogLevel(Enum):
    """Standard log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogContext:
    """Thread-local context for structured logging."""
    
    def __init__(self):
        self._local = threading.local()
    
    def set_context(self, **kwargs):
        """Set context variables for current thread."""
        if not hasattr(self._local, 'context'):
            self._local.context = {}
        self._local.context.update(kwargs)
    
    def get_context(self) -> Dict[str, Any]:
        """Get context variables for current thread."""
        if not hasattr(self._local, 'context'):
            self._local.context = {}
        return self._local.context.copy()
    
    def clear_context(self):
        """Clear context variables for current thread."""
        if hasattr(self._local, 'context'):
            self._local.context.clear()
    
    def remove_context(self, *keys):
        """Remove specific context keys."""
        if hasattr(self._local, 'context'):
            for key in keys:
                self._local.context.pop(key, None)


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def __init__(self, include_context: bool = True, json_format: bool = False):
        self.include_context = include_context
        self.json_format = json_format
        super().__init__()
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with structured data."""
        # Base log data
        log_data = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add thread and process info for debugging
        if record.levelno >= logging.DEBUG:
            log_data.update({
                'thread_id': record.thread,
                'process_id': record.process
            })
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # Add context from thread-local storage
        if self.include_context and hasattr(record, '_context'):
            log_data['context'] = record._context
        
        # Add extra fields from record
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info', '_context']:
                extra_fields[key] = value
        
        if extra_fields:
            log_data['extra'] = extra_fields
        
        if self.json_format:
            return json.dumps(log_data, default=str, ensure_ascii=False)
        else:
            # Human-readable format
            base_msg = f"[{log_data['timestamp']}] [{log_data['level']}] [{log_data['logger']}] {log_data['message']}"
            
            if 'context' in log_data and log_data['context']:
                context_str = " | ".join([f"{k}={v}" for k, v in log_data['context'].items()])
                base_msg += f" | Context: {context_str}"
            
            if 'extra' in log_data and log_data['extra']:
                extra_str = " | ".join([f"{k}={v}" for k, v in log_data['extra'].items()])
                base_msg += f" | Extra: {extra_str}"
            
            if 'exception' in log_data:
                base_msg += f"\n{log_data['exception']}"
            
            return base_msg


class ContextualLogger:
    """Logger wrapper that adds context to log records."""
    
    def __init__(self, logger: logging.Logger, context_manager: LogContext):
        self._logger = logger
        self._context_manager = context_manager
    
    def _log_with_context(self, level: int, msg: str, *args, **kwargs):
        """Log message with current context."""
        # Get current context
        context = self._context_manager.get_context()
        
        # Create log record
        if args:
            msg = msg % args
        
        # Add context to extra
        extra = kwargs.get('extra', {})
        if context:
            extra['_context'] = context
            kwargs['extra'] = extra
        
        # Log the message
        self._logger.log(level, msg, **kwargs)
    
    def debug(self, msg: str, *args, **kwargs):
        """Log debug message with context."""
        self._log_with_context(logging.DEBUG, msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        """Log info message with context."""
        self._log_with_context(logging.INFO, msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """Log warning message with context."""
        self._log_with_context(logging.WARNING, msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """Log error message with context."""
        self._log_with_context(logging.ERROR, msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """Log critical message with context."""
        self._log_with_context(logging.CRITICAL, msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        """Log exception with context."""
        kwargs['exc_info'] = True
        self._log_with_context(logging.ERROR, msg, *args, **kwargs)
    
    def set_context(self, **kwargs):
        """Set context for this logger."""
        self._context_manager.set_context(**kwargs)
    
    def clear_context(self):
        """Clear context for this logger."""
        self._context_manager.clear_context()
    
    def remove_context(self, *keys):
        """Remove specific context keys."""
        self._context_manager.remove_context(*keys)


class LoggerManager:
    """Centralized logger manager."""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._loggers: Dict[str, ContextualLogger] = {}
        self._context_manager = LogContext()
        self._setup_root_logger()
    
    def _setup_root_logger(self):
        """Setup root logger configuration."""
        # Get configuration
        log_level = config.get("logging.level", "INFO")
        log_format = config.get("logging.format", "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s")
        log_file_path = config.get("logging.file_path", "")
        max_file_size_mb = config.get("logging.max_file_size_mb", 10)
        backup_count = config.get("logging.backup_count", 5)
        json_format = config.get("logging.json_format", False)
        
        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Create formatter
        if json_format:
            formatter = StructuredFormatter(json_format=True)
        else:
            formatter = StructuredFormatter(json_format=False)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # File handler if configured
        if log_file_path:
            try:
                log_path = Path(log_file_path)
                log_path.parent.mkdir(parents=True, exist_ok=True)
                
                if max_file_size_mb > 0:
                    file_handler = RotatingFileHandler(
                        log_file_path,
                        maxBytes=max_file_size_mb * 1024 * 1024,
                        backupCount=backup_count
                    )
                else:
                    file_handler = TimedRotatingFileHandler(
                        log_file_path,
                        when='midnight',
                        backupCount=backup_count
                    )
                
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)
            except Exception as e:
                root_logger.warning(f"Failed to setup file logging: {e}")
        
        # Configure third-party loggers
        self._configure_third_party_loggers()
    
    def _configure_third_party_loggers(self):
        """Configure third-party library loggers."""
        # Celery loggers
        celery_loggers = [
            "celery", "celery.worker", "celery.task", "celery.redirected",
            "celery.worker.strategy", "celery.worker.consumer", 
            "celery.worker.heartbeat", "celery.worker.control",
            "celery.bootsteps", "kombu", "amqp"
        ]
        
        for logger_name in celery_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.WARNING)
            logger.propagate = False
        
        # Other third-party loggers
        third_party_configs = {
            "urllib3": logging.WARNING,
            "requests": logging.WARNING,
            "sqlalchemy": logging.WARNING,
            "alembic": logging.WARNING,
        }
        
        for logger_name, level in third_party_configs.items():
            logger = logging.getLogger(logger_name)
            logger.setLevel(level)
    
    def get_logger(self, name: str) -> ContextualLogger:
        """Get or create a contextual logger."""
        if name not in self._loggers:
            base_logger = logging.getLogger(name)
            self._loggers[name] = ContextualLogger(base_logger, self._context_manager)
        
        return self._loggers[name]
    
    def set_global_context(self, **kwargs):
        """Set global context for all loggers."""
        self._context_manager.set_context(**kwargs)
    
    def clear_global_context(self):
        """Clear global context."""
        self._context_manager.clear_context()


# Global logger manager instance
_logger_manager = LoggerManager()


def get_logger(name: str = None) -> ContextualLogger:
    """
    Get a contextual logger instance.
    
    Args:
        name: Logger name (defaults to caller's module name)
        
    Returns:
        ContextualLogger instance
    """
    if name is None:
        # Get caller's module name
        frame = sys._getframe(1)
        name = frame.f_globals.get('__name__', 'unknown')
    
    return _logger_manager.get_logger(name)


def set_global_context(**kwargs):
    """Set global logging context."""
    _logger_manager.set_global_context(**kwargs)


def clear_global_context():
    """Clear global logging context."""
    _logger_manager.clear_global_context()


# Convenience function for backward compatibility
def setup_logging():
    """Setup logging (for backward compatibility)."""
    # Logger manager is automatically initialized
    pass
