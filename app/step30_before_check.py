import os, sys, logging
from app.step31_objectDetection import step31_objectDetection_run
import app.helper as helper
from app.job_tracking import job_tracker

from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)
def step30_before_check_run(job_uuid, task_params_dict:dict, detection_file):
    try:
        job_tracker.update_job_state(job_uuid, "objectdetection", "started")
        if os.path.isfile(detection_file):
            step31_objectDetection_run(job_uuid, task_params_dict, detection_file)
        else:
            err_msg = f"File not found {detection_file}"
            raise Exception(err_msg)
    except Exception as err:
        job_tracker.update_job_state(job_uuid, "objectdetection", "failed")
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"Exception with error: {err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
    finally:
        job_tracker.update_job_state(job_uuid, "objectdetection", "finished")


