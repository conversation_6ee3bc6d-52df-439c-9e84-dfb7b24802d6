from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select
from app.database import db_manager, Job, JobResult
from app.config.settings import config
from app.config.logging_config import setup_logging
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

setup_logging()
logger = logging.getLogger(__name__)

class JobTracker:
    def __init__(self):
        self._init_postgresql()

    def _init_postgresql(self):
        try:
            logger.info("PostgreSQL job tracker initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL job tracker: {str(e)}")
            raise
    
    def job_exists(self, uuid: str) -> bool:
        try:
            with db_manager.get_session() as db:
                job = db.query(Job).filter(Job.uuid == uuid).first()
                return job is not None
        except SQLAlchemyError as e:
            logger.error(f"Error checking job existence for UUID {uuid}: {str(e)}")
            return True
    
    def create_job(self, uuid: str, ot_traceid: str, ot_spanid: str) -> bool:
        try:
            if self.job_exists(uuid):
                return False

            retention_days = config.get("job_tracking.retention_days", 7)
            expires_at = datetime.utcnow() + timedelta(days=retention_days)

            with db_manager.get_session() as db:
                job = Job(
                    uuid=uuid,
                    state="queued",
                    task_id=None,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    expires_at=expires_at,
                    ot_traceid=ot_traceid,
                    ot_spanid=ot_spanid
                )
                db.add(job)
                db.commit()

            logger.info(f"Job {uuid} created successfully")
            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating job {uuid}: {str(e)}")
            return False

    def create_job_result(self, job_uuid: str, result: dict) -> bool:
        try:
            with db_manager.get_session() as db:
                job = db.query(Job).filter(Job.uuid == job_uuid).first()
                if not job:
                    logger.warning(f"Job {job_uuid} not found, cannot insert result")
                    return False

                job_result = JobResult(
                    job_uuid=job_uuid,
                    result=result,
                    created_at=datetime.utcnow()
                )
                db.add(job_result)
                db.commit()
            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating JobResult for {job_uuid}: {str(e)}")
            return False
    
    def update_job_state(self, uuid: str, step: str, state: str, error_message: Optional[str] = None, task_id: Optional[str] = None):
        try:
            with db_manager.get_session() as db:
                job = db.query(Job).filter(Job.uuid == uuid).first()
                if job:
                    job.state = state
                    job.step = step
                    job.error_message = error_message
                    job.updated_at = datetime.utcnow()
                    if task_id:
                        job.task_id = task_id
                    db.commit()

            upperState = state.upper()
            upperStep = step.upper()
            logger.info(f"Job {uuid} state: {upperStep}/{upperState}")
        except SQLAlchemyError as e:
            logger.error(f"Error updating job {uuid} state: {str(e)}")
    
    def get_job(self, uuid: str) -> Optional[Dict[str, Any]]:
        try:
            with db_manager.get_session() as db:
                job = db.query(Job).filter(Job.uuid == uuid).first()
                if job:
                    return {
                        "uuid": job.uuid,
                        "state": job.state,
                        "step": job.step,
                        "task_id": job.task_id,
                        "created_at": job.created_at,
                        "updated_at": job.updated_at,
                        "expires_at": job.expires_at,
                        "ot_traceid": job.ot_traceid,
                        "ot_spanid": job.ot_spanid,
                        "error_message": job.error_message
                    }
                return None
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving job {uuid}: {str(e)}")
            return None

    def get_job_state_by_uuid(self, job_uuid: str) -> str | None:
        try:
            with db_manager.get_session() as db:
                stmt = select(Job.state).where(Job.uuid == job_uuid)
                row = db.execute(stmt).scalar_one_or_none()
                return row  # state string veya None
        except SQLAlchemyError as e:
            logger.error(f"Error fetching state for job {job_uuid}: {str(e)}")
            return None

    def get_jobs_by_state(self, state: str) -> list:
        try:
            with db_manager.get_session() as db:
                jobs = db.query(Job).filter(Job.state == state).all()
                return [
                    {
                        "uuid": job.uuid,
                        "state": job.state,
                        "step": job.step,
                        "task_id": job.task_id,
                        "created_at": job.created_at,
                        "updated_at": job.updated_at,
                        "expires_at": job.expires_at,
                        "ot_traceid": job.ot_traceid,
                        "ot_spanid": job.ot_spanid,
                        "error_message": job.error_message
                    }
                    for job in jobs
                ]
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving jobs with state {state}: {str(e)}")
            return []

    def get_job_results(self, job_uuid: str):
        try:
            with db_manager.get_session() as db:
                stmt = (
                    select(Job.state, JobResult.result, Job.error_message, JobResult.result["class"].astext.label("class"))
                    .join(JobResult, Job.uuid == JobResult.job_uuid)
                    .where(Job.uuid == job_uuid)
                )
                rows = db.execute(stmt).all()

                if not rows:
                    logger.info(f"No results found for job {job_uuid}")
                    return []

                results = [
                    {"state": state, "result": result, "error_message": error_message, "class": class_name}
                    for state, result, error_message, class_name in rows
                ]
                return results

        except SQLAlchemyError as e:
            logger.error(f"Error fetching job results for {job_uuid}: {str(e)}")
            return []

    def get_job_count_by_state(self, state: str) -> int:
        try:
            with db_manager.get_session() as db:
                return db.query(Job).filter(Job.state == state).count()
        except SQLAlchemyError as e:
            logger.error(f"Error counting jobs with state {state}: {str(e)}")
            return 0
    
    def cleanup_expired_jobs(self):
        try:
            with db_manager.get_session() as db:
                current_time = datetime.utcnow()
                expired_jobs = db.query(Job).filter(Job.expires_at < current_time).all()
                count = len(expired_jobs)

                for job in expired_jobs:
                    db.delete(job)

                db.commit()
                logger.info(f"Cleaned up {count} expired jobs from PostgreSQL")
        except SQLAlchemyError as e:
            logger.error(f"Error cleaning up expired jobs: {str(e)}")

job_tracker = JobTracker()